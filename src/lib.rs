//! # ComfyUI Inference Engine
//!
//! A high-performance Rust-based deep learning inference engine for Transformer architectures.
//!
//! This library provides:
//! - Efficient tensor operations with SIMD optimization
//! - Multi-head attention mechanisms with KV caching
//! - Transformer encoder/decoder blocks
//! - Model configuration and management
//! - High-throughput inference engine
//! - Python bindings for easy integration
//!
//! ## Features
//!
//! - **Zero-copy operations**: Minimize memory allocation and data copying
//! - **SIMD optimization**: Vectorized operations for maximum performance
//! - **Parallel computing**: Multi-threaded execution with work-stealing
//! - **Memory safety**: Rust's ownership system prevents memory errors
//! - **Type safety**: Compile-time and runtime shape validation
//! - **Device abstraction**: Support for CPU, CUDA, and Metal backends
//!
//! ## Quick Start
//!
//! ```rust
//! use comfyui_inference_engine::prelude::*;
//!
//! // Create a simple transformer configuration
//! let config = TransformerConfig::default();
//! println!("Config created with vocab_size: {}", config.vocab_size);
//! # Ok::<(), Box<dyn std::error::Error>>(())
//! ```

#![warn(clippy::all)]
#![allow(clippy::type_complexity)]
#![allow(missing_docs)]

// Core modules
pub mod error;
pub mod tensor;
pub mod layers;
pub mod attention;
pub mod transformer;
pub mod model;
pub mod inference;
pub mod utils;

// Re-exports for convenience
pub mod prelude {
    //! Common imports for using the inference engine.

    pub use crate::error::*;
    pub use crate::tensor::{Tensor, TensorOps, Shape, DType};
    pub use crate::layers::*;
    pub use crate::attention::*;
    pub use crate::transformer::*;
    pub use crate::model::*;
    pub use crate::inference::*;
    // pub use crate::utils::*;
}

// Optional feature modules
#[cfg(feature = "python-bindings")]
pub mod python;

#[cfg(feature = "cuda")]
pub mod cuda;

#[cfg(feature = "metal")]
pub mod metal;

// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Initialize the inference engine with default settings.
///
/// This function sets up logging, thread pools, and other global state.
/// It should be called once at the beginning of your application.
///
/// # Examples
///
/// ```rust
/// use comfyui_inference_engine::init;
///
/// fn main() -> Result<(), Box<dyn std::error::Error>> {
///     init()?;
///     // Your application code here
///     Ok(())
/// }
/// ```
pub fn init() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    env_logger::init();

    // Initialize thread pool
    rayon::ThreadPoolBuilder::new()
        .num_threads(num_cpus::get())
        .build_global()?;

    log::info!("ComfyUI Inference Engine v{} initialized", VERSION);
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_init() {
        assert!(init().is_ok());
    }

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }
}
