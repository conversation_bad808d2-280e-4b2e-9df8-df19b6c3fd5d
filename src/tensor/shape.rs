//! Tensor shape and stride management.

use std::fmt;
use crate::error::{TensorError, <PERSON><PERSON>r<PERSON>ontext};

/// Represents the shape and memory layout of a tensor.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub struct Shape {
    /// Dimensions of the tensor.
    dims: Vec<usize>,
    /// Strides for each dimension (in elements, not bytes).
    strides: Vec<usize>,
    /// Total number of elements.
    size: usize,
}

impl Shape {
    /// Create a new shape with the given dimensions.
    /// 
    /// # Examples
    /// 
    /// ```
    /// use comfyui_inference_engine::tensor::Shape;
    /// 
    /// let shape = Shape::new(vec![2, 3, 4]);
    /// assert_eq!(shape.dims(), &[2, 3, 4]);
    /// assert_eq!(shape.size(), 24);
    /// ```
    pub fn new(dims: Vec<usize>) -> Self {
        let strides = Self::compute_row_major_strides(&dims);
        let size = dims.iter().product();
        
        Self {
            dims,
            strides,
            size,
        }
    }
    
    /// Create a new shape with custom strides.
    pub fn with_strides(dims: Vec<usize>, strides: Vec<usize>) -> Result<Self, TensorError> {
        if dims.len() != strides.len() {
            return Err(TensorError::InvalidDimension {
                dimension: strides.len(),
                total_dims: dims.len(),
                context: Some(ErrorContext::new("shape_with_strides", "tensor::shape")),
            });
        }
        
        let size = dims.iter().product();
        
        Ok(Self {
            dims,
            strides,
            size,
        })
    }
    
    /// Get the dimensions of this shape.
    pub fn dims(&self) -> &[usize] {
        &self.dims
    }
    
    /// Get the strides of this shape.
    pub fn strides(&self) -> &[usize] {
        &self.strides
    }
    
    /// Get the total number of elements.
    pub fn size(&self) -> usize {
        self.size
    }
    
    /// Get the number of dimensions (rank).
    pub fn rank(&self) -> usize {
        self.dims.len()
    }
    
    /// Check if this shape represents a scalar (0 dimensions).
    pub fn is_scalar(&self) -> bool {
        self.dims.is_empty()
    }
    
    /// Check if this shape represents a vector (1 dimension).
    pub fn is_vector(&self) -> bool {
        self.dims.len() == 1
    }
    
    /// Check if this shape represents a matrix (2 dimensions).
    pub fn is_matrix(&self) -> bool {
        self.dims.len() == 2
    }
    
    /// Check if the tensor with this shape is contiguous in memory.
    pub fn is_contiguous(&self) -> bool {
        let expected_strides = Self::compute_row_major_strides(&self.dims);
        self.strides == expected_strides
    }
    
    /// Compute the linear index for the given multi-dimensional indices.
    pub fn compute_linear_index(&self, indices: &[usize]) -> usize {
        assert_eq!(indices.len(), self.dims.len());
        
        indices.iter()
            .zip(self.strides.iter())
            .map(|(&idx, &stride)| idx * stride)
            .sum()
    }
    
    /// Convert a linear index to multi-dimensional indices.
    pub fn linear_to_indices(&self, linear_index: usize) -> Vec<usize> {
        let mut indices = vec![0; self.dims.len()];
        let mut remaining = linear_index;
        
        for i in 0..self.dims.len() {
            indices[i] = remaining / self.strides[i];
            remaining %= self.strides[i];
        }
        
        indices
    }
    
    /// Reshape to a new shape with the same total size.
    pub fn reshape(&self, new_dims: &[usize]) -> Result<Shape, TensorError> {
        let new_size: usize = new_dims.iter().product();
        
        if new_size != self.size {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.size],
                actual: vec![new_size],
                context: Some(ErrorContext::new("reshape", "tensor::shape")
                    .with_info("original_shape", format!("{:?}", self.dims))
                    .with_info("new_shape", format!("{:?}", new_dims))),
            });
        }
        
        Ok(Shape::new(new_dims.to_vec()))
    }
    
    /// Transpose by swapping two dimensions.
    pub fn transpose(&self, dim1: usize, dim2: usize) -> Result<Shape, TensorError> {
        if dim1 >= self.dims.len() || dim2 >= self.dims.len() {
            return Err(TensorError::InvalidDimension {
                dimension: dim1.max(dim2),
                total_dims: self.dims.len(),
                context: Some(ErrorContext::new("transpose", "tensor::shape")),
            });
        }
        
        let mut new_dims = self.dims.clone();
        let mut new_strides = self.strides.clone();
        
        new_dims.swap(dim1, dim2);
        new_strides.swap(dim1, dim2);
        
        Shape::with_strides(new_dims, new_strides)
    }
    
    /// Permute dimensions according to the given order.
    pub fn permute(&self, dims: &[usize]) -> Result<Shape, TensorError> {
        if dims.len() != self.dims.len() {
            return Err(TensorError::InvalidDimension {
                dimension: dims.len(),
                total_dims: self.dims.len(),
                context: Some(ErrorContext::new("permute", "tensor::shape")),
            });
        }
        
        // Check that all dimensions are valid and unique
        let mut sorted_dims = dims.to_vec();
        sorted_dims.sort_unstable();
        let expected: Vec<usize> = (0..self.dims.len()).collect();
        
        if sorted_dims != expected {
            return Err(TensorError::InvalidDimension {
                dimension: dims.len(),
                total_dims: self.dims.len(),
                context: Some(ErrorContext::new("permute", "tensor::shape")
                    .with_info("permutation", format!("{:?}", dims))),
            });
        }
        
        let new_dims: Vec<usize> = dims.iter().map(|&i| self.dims[i]).collect();
        let new_strides: Vec<usize> = dims.iter().map(|&i| self.strides[i]).collect();
        
        Shape::with_strides(new_dims, new_strides)
    }
    
    /// Remove dimensions of size 1.
    pub fn squeeze(&self, dim: Option<usize>) -> Result<Shape, TensorError> {
        match dim {
            Some(d) => {
                if d >= self.dims.len() {
                    return Err(TensorError::InvalidDimension {
                        dimension: d,
                        total_dims: self.dims.len(),
                        context: Some(ErrorContext::new("squeeze", "tensor::shape")),
                    });
                }
                
                if self.dims[d] != 1 {
                    return Err(TensorError::InvalidDimension {
                        dimension: self.dims[d],
                        total_dims: 1,
                        context: Some(ErrorContext::new("squeeze", "tensor::shape")
                            .with_info("dimension_size", self.dims[d].to_string())),
                    });
                }
                
                let mut new_dims = self.dims.clone();
                new_dims.remove(d);
                Ok(Shape::new(new_dims))
            }
            None => {
                let new_dims: Vec<usize> = self.dims.iter()
                    .copied()
                    .filter(|&size| size != 1)
                    .collect();
                Ok(Shape::new(new_dims))
            }
        }
    }
    
    /// Add a dimension of size 1 at the specified position.
    pub fn unsqueeze(&self, dim: usize) -> Result<Shape, TensorError> {
        if dim > self.dims.len() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.dims.len() + 1,
                context: Some(ErrorContext::new("unsqueeze", "tensor::shape")),
            });
        }
        
        let mut new_dims = self.dims.clone();
        new_dims.insert(dim, 1);
        Ok(Shape::new(new_dims))
    }
    
    /// Broadcast this shape with another shape.
    pub fn broadcast_with(&self, other: &Shape) -> Result<Shape, TensorError> {
        let max_rank = self.rank().max(other.rank());
        let mut result_dims = vec![1; max_rank];
        
        // Align shapes from the right (trailing dimensions)
        let self_offset = max_rank - self.rank();
        let other_offset = max_rank - other.rank();
        
        for i in 0..max_rank {
            let self_dim = if i >= self_offset {
                self.dims[i - self_offset]
            } else {
                1
            };
            
            let other_dim = if i >= other_offset {
                other.dims[i - other_offset]
            } else {
                1
            };
            
            if self_dim == 1 {
                result_dims[i] = other_dim;
            } else if other_dim == 1 {
                result_dims[i] = self_dim;
            } else if self_dim == other_dim {
                result_dims[i] = self_dim;
            } else {
                return Err(TensorError::ShapeMismatch {
                    expected: vec![self_dim],
                    actual: vec![other_dim],
                    context: Some(ErrorContext::new("broadcast", "tensor::shape")
                        .with_info("self_shape", format!("{:?}", self.dims))
                        .with_info("other_shape", format!("{:?}", other.dims))),
                });
            }
        }
        
        Ok(Shape::new(result_dims))
    }
    
    /// Compute row-major strides for the given dimensions.
    fn compute_row_major_strides(dims: &[usize]) -> Vec<usize> {
        let mut strides = vec![1; dims.len()];
        
        for i in (0..dims.len().saturating_sub(1)).rev() {
            strides[i] = strides[i + 1] * dims[i + 1];
        }
        
        strides
    }
}

impl fmt::Display for Shape {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Shape({:?})", self.dims)
    }
}

impl From<Vec<usize>> for Shape {
    fn from(dims: Vec<usize>) -> Self {
        Shape::new(dims)
    }
}

impl From<&[usize]> for Shape {
    fn from(dims: &[usize]) -> Self {
        Shape::new(dims.to_vec())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_shape_creation() {
        let shape = Shape::new(vec![2, 3, 4]);
        assert_eq!(shape.dims(), &[2, 3, 4]);
        assert_eq!(shape.size(), 24);
        assert_eq!(shape.rank(), 3);
        assert_eq!(shape.strides(), &[12, 4, 1]);
    }
    
    #[test]
    fn test_shape_properties() {
        let scalar = Shape::new(vec![]);
        assert!(scalar.is_scalar());
        
        let vector = Shape::new(vec![5]);
        assert!(vector.is_vector());
        
        let matrix = Shape::new(vec![3, 4]);
        assert!(matrix.is_matrix());
    }
    
    #[test]
    fn test_linear_index() {
        let shape = Shape::new(vec![2, 3, 4]);
        assert_eq!(shape.compute_linear_index(&[0, 0, 0]), 0);
        assert_eq!(shape.compute_linear_index(&[0, 0, 1]), 1);
        assert_eq!(shape.compute_linear_index(&[0, 1, 0]), 4);
        assert_eq!(shape.compute_linear_index(&[1, 0, 0]), 12);
    }
    
    #[test]
    fn test_reshape() {
        let shape = Shape::new(vec![2, 3, 4]);
        let reshaped = shape.reshape(&[6, 4]).unwrap();
        assert_eq!(reshaped.dims(), &[6, 4]);
        assert_eq!(reshaped.size(), 24);
    }
    
    #[test]
    fn test_transpose() {
        let shape = Shape::new(vec![2, 3, 4]);
        let transposed = shape.transpose(0, 2).unwrap();
        assert_eq!(transposed.dims(), &[4, 3, 2]);
    }
    
    #[test]
    fn test_broadcast() {
        let shape1 = Shape::new(vec![3, 1]);
        let shape2 = Shape::new(vec![1, 4]);
        let broadcasted = shape1.broadcast_with(&shape2).unwrap();
        assert_eq!(broadcasted.dims(), &[3, 4]);
    }
}
